uuid: 4ceda7c4-0bb3-4acf-94d8-798ae8895bcb
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_show_title
    - paragraphs.paragraphs_type.text_and_image
id: paragraph.text_and_image.field_show_title
field_name: field_show_title
entity_type: paragraph
bundle: text_and_image
label: 'Show title'
description: "Select the checkbox for this section title to display on the website. \r\nRemove the checkbox for this section if you do not want the section title to display on the website."
required: false
translatable: true
default_value:
  -
    value: 0
default_value_callback: ''
settings:
  on_label: 'On'
  off_label: 'Off'
field_type: boolean
