uuid: c64f93cc-5a0e-4775-afe1-f4d3a7f5706b
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.media.full
    - core.entity_view_mode.media.responsive_image
    - core.entity_view_mode.media.responsive_image_full_quarter_
    - core.entity_view_mode.media.responsive_image_full_third_
    - core.entity_view_mode.media.responsive_image_half
    - core.entity_view_mode.media.responsive_image_half_width_
    - core.entity_view_mode.media.responsive_image_third_half_
    - core.entity_view_mode.media.responsive_image_two_third_width_
  module:
    - cam_custom
    - editor
    - linkit
    - media
_core:
  default_config_hash: WNeK5FbcY8pXgEpbD_KgRzlF1-5PL3BJXwqaBctPTqw
name: 'Full HTML'
format: full_html
weight: 2
filters:
  cam_custom_tablewrapper:
    id: cam_custom_tablewrapper
    provider: cam_custom
    status: true
    weight: -10
    settings: {  }
  editor_file_reference:
    id: editor_file_reference
    provider: editor
    status: true
    weight: 11
    settings: {  }
  filter_align:
    id: filter_align
    provider: filter
    status: true
    weight: 8
    settings: {  }
  filter_autop:
    id: filter_autop
    provider: filter
    status: true
    weight: 0
    settings: {  }
  filter_caption:
    id: filter_caption
    provider: filter
    status: true
    weight: 9
    settings: {  }
  filter_html:
    id: filter_html
    provider: filter
    status: false
    weight: -10
    settings:
      allowed_html: '<p class="blockquote-attr">'
      filter_html_help: true
      filter_html_nofollow: false
  filter_htmlcorrector:
    id: filter_htmlcorrector
    provider: filter
    status: true
    weight: 10
    settings: {  }
  filter_url:
    id: filter_url
    provider: filter
    status: true
    weight: 0
    settings:
      filter_url_length: 72
  linkit:
    id: linkit
    provider: linkit
    status: true
    weight: 0
    settings:
      title: true
  media_embed:
    id: media_embed
    provider: media
    status: true
    weight: 100
    settings:
      default_view_mode: responsive_image
      allowed_view_modes:
        default: default
        full: full
        responsive_image: responsive_image
        responsive_image_full_quarter_: responsive_image_full_quarter_
        responsive_image_full_third_: responsive_image_full_third_
        responsive_image_half: responsive_image_half
        responsive_image_half_width_: responsive_image_half_width_
        responsive_image_third_half_: responsive_image_third_half_
        responsive_image_two_third_width_: responsive_image_two_third_width_
      allowed_media_types:
        image: image
        remote_video: remote_video
