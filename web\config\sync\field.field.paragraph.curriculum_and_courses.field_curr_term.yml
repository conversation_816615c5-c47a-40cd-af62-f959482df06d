uuid: 4e631097-e7af-4ab7-9d33-3e41a3143ed5
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_curr_term
    - paragraphs.paragraphs_type.curriculum_and_courses
    - paragraphs.paragraphs_type.curriculum_and_courses_term
  module:
    - entity_reference_revisions
id: paragraph.curriculum_and_courses.field_curr_term
field_name: field_curr_term
entity_type: paragraph
bundle: curriculum_and_courses
label: Term
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    negate: 0
    target_bundles:
      curriculum_and_courses_term: curriculum_and_courses_term
    target_bundles_drag_drop:
      call_to_action:
        weight: 19
        enabled: false
      call_to_action_button:
        weight: 20
        enabled: false
      contact:
        weight: 21
        enabled: false
      contact_campus_location:
        weight: 22
        enabled: false
      curriculum_and_courses:
        weight: 23
        enabled: false
      curriculum_and_courses_list:
        weight: 24
        enabled: false
      curriculum_and_courses_term:
        enabled: true
        weight: 25
      image:
        weight: 26
        enabled: false
      popular_item:
        weight: 27
        enabled: false
      program_requirement:
        weight: 28
        enabled: false
      related_information:
        weight: 29
        enabled: false
      requisite_list:
        weight: 30
        enabled: false
      requisite_list_item:
        weight: 31
        enabled: false
      requisites:
        weight: 32
        enabled: false
      section_links:
        weight: 33
        enabled: false
      text_and_image:
        weight: 34
        enabled: false
      text_and_video:
        weight: 35
        enabled: false
      text_only:
        weight: 36
        enabled: false
field_type: entity_reference_revisions
