uuid: 55de3d82-c511-4a77-a511-e06574eb1003
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_sport
    - paragraphs.paragraphs_type.coach_profile
    - taxonomy.vocabulary.sport
id: paragraph.coach_profile.field_sport
field_name: field_sport
entity_type: paragraph
bundle: coach_profile
label: Sport
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      sport: sport
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
