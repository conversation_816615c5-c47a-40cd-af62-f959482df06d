uuid: e09eb564-c0e4-4234-9e63-0724cf12eab0
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_link
    - paragraphs.paragraphs_type.popular_item
  module:
    - link
id: paragraph.popular_item.field_link
field_name: field_link
entity_type: paragraph
bundle: popular_item
label: Link
description: "Link URL: Start typing an internal page URL to add a popular (top task) link\r\nLink Title: Only edit if page title isn't task focused i.e. 'Apply for a license'"
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  link_type: 17
  title: 2
field_type: link
