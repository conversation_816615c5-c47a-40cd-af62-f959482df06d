uuid: 3108fd79-de5d-429d-b3e6-5986eab73cb5
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_display_name
    - paragraphs.paragraphs_type.student_athlete_profile
id: paragraph.student_athlete_profile.field_display_name
field_name: field_display_name
entity_type: paragraph
bundle: student_athlete_profile
label: 'Display name'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
