uuid: e5b014eb-1f9f-4ca0-9318-8082db8c4ff8
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_image
    - media.type.image
    - paragraphs.paragraphs_type.custom_featured_events_list
  content:
    - 'media:image:962954d7-8f88-4d55-9856-cf88ac7e5571'
id: paragraph.custom_featured_events_list.field_image
field_name: field_image
entity_type: paragraph
bundle: custom_featured_events_list
label: 'Complimentary Image'
description: ''
required: false
translatable: true
default_value:
  -
    target_uuid: 962954d7-8f88-4d55-9856-cf88ac7e5571
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      image: image
    sort:
      field: _none
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
