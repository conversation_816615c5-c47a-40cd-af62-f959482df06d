uuid: 5723936c-ad02-435f-bfb9-09a4934052f9
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_image
    - media.type.image
    - paragraphs.paragraphs_type.image
id: paragraph.image.field_image
field_name: field_image
entity_type: paragraph
bundle: image
label: Image
description: "Upload an image (any format). Ensure the image has been compressed and optimized for web use (use a tool like compressjpeg.com or ImageOptim).\r\nImage size should not exceed 1MB.\r\nImage should be at least 640px in width."
required: true
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      image: image
    sort:
      field: _none
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
