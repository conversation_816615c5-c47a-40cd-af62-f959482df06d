uuid: e43bbf42-4fb2-4a88-8071-2c8e4aa817e3
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_school
    - paragraphs.paragraphs_type.college_event
    - taxonomy.vocabulary.schools
id: paragraph.college_event.field_school
field_name: field_school
entity_type: paragraph
bundle: college_event
label: School
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      schools: schools
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
