uuid: 905b3f4e-3caf-4d25-a081-b587e7441e5e
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_news_item
    - node.type.article
    - paragraphs.paragraphs_type.showcase_primary_content
id: paragraph.showcase_primary_content.field_news_item
field_name: field_news_item
entity_type: paragraph
bundle: showcase_primary_content
label: 'Primary News Item'
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      article: article
    sort:
      field: _none
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
