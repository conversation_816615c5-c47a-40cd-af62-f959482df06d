uuid: a7531498-733f-4246-a6a3-4323c550851e
langcode: en
status: true
dependencies:
  module:
    - node
id: default_profile
label: 'Default profile'
description: ''
matchers:
  22298697-45eb-4261-be8c-52810cdf33ac:
    uuid: 22298697-45eb-4261-be8c-52810cdf33ac
    id: 'entity:node'
    weight: 0
    settings:
      result_description: ''
      bundles: {  }
      group_by_bundle: true
      include_unpublished: true
      metadata: ''
      substitution_type: canonical
      limit: 100
  7793617b-5802-4fc1-9e9c-ce0ec2cebf7f:
    uuid: 7793617b-5802-4fc1-9e9c-ce0ec2cebf7f
    id: 'entity:media'
    weight: 0
    settings:
      result_description: ''
      bundles:
        document: document
        file: file
      group_by_bundle: true
      metadata: ''
      substitution_type: media
      limit: 100
  700c4bfc-a095-46f6-bdcb-169be70fbed3:
    uuid: 700c4bfc-a095-46f6-bdcb-169be70fbed3
    id: email
    weight: 0
    settings: {  }
