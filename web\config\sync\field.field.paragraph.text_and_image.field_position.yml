uuid: a776f728-e99a-41d9-aef3-bd207edf9f37
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_position
    - paragraphs.paragraphs_type.text_and_image
  module:
    - options
id: paragraph.text_and_image.field_position
field_name: field_position
entity_type: paragraph
bundle: text_and_image
label: 'Position (image)'
description: 'Select image position: left or right'
required: true
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: list_string
