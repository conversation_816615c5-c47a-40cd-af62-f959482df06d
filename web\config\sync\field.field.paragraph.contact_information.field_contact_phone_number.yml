uuid: 4ac7fcfe-7d16-4b0e-b218-a039b7e4187d
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_contact_phone_number
    - paragraphs.paragraphs_type.contact_information
  module:
    - telephone
id: paragraph.contact_information.field_contact_phone_number
field_name: field_contact_phone_number
entity_type: paragraph
bundle: contact_information
label: 'Phone number'
description: 'Enter the phone number of the contact person.'
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: telephone
