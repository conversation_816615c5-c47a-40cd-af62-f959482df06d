uuid: e4b804e7-00f1-4082-8a31-49c88369eb05
langcode: en
status: true
dependencies:
  module:
    - menu_ui
    - scheduler
third_party_settings:
  menu_ui:
    available_menus:
      - main
    parent: 'main:menu_link_content:15994b0f-b9ad-46b7-96b0-065c6389491d'
  scheduler:
    expand_fieldset: when_required
    fields_display_mode: vertical_tab
    publish_enable: true
    publish_past_date: error
    publish_required: false
    publish_revision: true
    publish_touch: false
    unpublish_enable: true
    unpublish_required: false
    unpublish_revision: false
    publish_past_date_created: false
    show_message_after_update: false
_core:
  default_config_hash: AeW1SEDgb1OTQACAWGhzvMknMYAJlcZu0jljfeU3oso
name: News
type: article
description: 'Use <em>news</em> for time-sensitive content like news, press releases or blog posts.'
help: null
new_revision: true
preview_mode: 1
display_submitted: false
