uuid: 0cd472a0-fda4-44f9-97d4-0c1fcb31085e
langcode: en
status: true
dependencies:
  module:
    - node
id: node.field_secondary_content_header
field_name: field_secondary_content_header
entity_type: node
type: string
settings:
  max_length: 70
  is_ascii: false
  case_sensitive: false
module: core
locked: false
cardinality: 1
translatable: true
indexes: {  }
persist_with_no_fields: false
custom_storage: false
