uuid: d4367a65-fc5e-482f-9b2d-484bc19a54ab
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_contact_email_address
    - paragraphs.paragraphs_type.contact_information
id: paragraph.contact_information.field_contact_email_address
field_name: field_contact_email_address
entity_type: paragraph
bundle: contact_information
label: 'Email address'
description: 'Enter the email address of the contact person.'
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: email
