uuid: d0015b72-66d8-436e-a0ac-c7bffcb58a95
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_sport
    - paragraphs.paragraphs_type.student_athlete_profile
    - taxonomy.vocabulary.sport
id: paragraph.student_athlete_profile.field_sport
field_name: field_sport
entity_type: paragraph
bundle: student_athlete_profile
label: Sport
description: ''
required: true
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      sport: sport
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
