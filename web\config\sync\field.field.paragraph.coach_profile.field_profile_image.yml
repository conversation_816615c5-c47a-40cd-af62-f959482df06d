uuid: a68191d2-a56d-4ae1-92f0-51325e5c8fbf
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_profile_image
    - media.type.image
    - paragraphs.paragraphs_type.coach_profile
id: paragraph.coach_profile.field_profile_image
field_name: field_profile_image
entity_type: paragraph
bundle: coach_profile
label: 'Profile image'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      image: image
    sort:
      field: _none
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
