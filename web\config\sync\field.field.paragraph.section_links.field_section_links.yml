uuid: *************-49a7-8827-c097cfa6e05c
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_section_links
    - paragraphs.paragraphs_type.section_links
  module:
    - link
id: paragraph.section_links.field_section_links
field_name: field_section_links
entity_type: paragraph
bundle: section_links
label: 'Section links'
description: "Link URL: Start typing an internal page URL to add a popular (top task) link\r\nLink Title: Only edit if page title isn't task focused i.e. 'Apply for a license'"
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  link_type: 17
  title: 2
field_type: link
