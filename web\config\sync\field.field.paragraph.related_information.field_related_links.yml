uuid: 6a64bbed-980a-47de-904b-a9f27e3a1d47
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_related_links
    - paragraphs.paragraphs_type.related_information
  module:
    - link
id: paragraph.related_information.field_related_links
field_name: field_related_links
entity_type: paragraph
bundle: related_information
label: Links
description: "Link URL: Start typing an internal page URL to add a popular (top task) link\r\nLink Title: Only edit if page title isn't task focused i.e. 'Apply for a license'"
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  link_type: 17
  title: 2
field_type: link
