uuid: 00a3bb0b-ca7f-4e6c-b386-1591b956b97a
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_event_items
    - node.type.event
    - paragraphs.paragraphs_type.custom_featured_events_list
id: paragraph.custom_featured_events_list.field_event_items
field_name: field_event_items
entity_type: paragraph
bundle: custom_featured_events_list
label: 'Event Items'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      event: event
    sort:
      field: _none
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
