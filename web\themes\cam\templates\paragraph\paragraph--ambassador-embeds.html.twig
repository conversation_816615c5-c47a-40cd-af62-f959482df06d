{%
  set classes = [
    'paragraph',
    'paragraph--type--' ~ paragraph.bundle|clean_class,
    view_mode ? 'paragraph--view-mode--' ~ view_mode|clean_class,
  ]
%}

{# {{ kint() }} #}
{% block paragraph %}
  <div{{ attributes.addClass(classes) }}>
    {% block content %}
    {% set widget = content.field_ambassador_embeds.0['#markup'] %}

          {% if widget == 'popcard' %}
             {# Process the multi-value field_popcard_terms into arrays for JavaScript and query string #}
             {% set terms_array = [] %}
             {% set query_params = [] %}
             {% for item in content.field_popcard_terms %}
               {% if item['#markup'] is not empty %}
                 {% set term_value = item['#markup']|striptags|trim %}
                 {% if term_value %}
                   {% set terms_array = terms_array|merge(['"' ~ term_value|e('js') ~ '"']) %}
                   {% set query_params = query_params|merge(['searchTerms=' ~ term_value|url_encode]) %}
                 {% endif %}
               {% endif %}
             {% endfor %}

             {# Create JavaScript array string and query string #}
             {% set js_terms_array = '[' ~ terms_array|join(',') ~ ']' %}
             {% set query_string = query_params|join('&') %}

            <script>(function(w,d,s,o,f,js,fjs){
              w['popCard']=o;w[o]=w[o]||function(){(w[o].q=w[o].q||[]).push(arguments)};          js=d.createElement(s),fjs=d.getElementsByTagName(s)[0];
              js.id=o;js.src=f;js.async=1;fjs.parentNode.insertBefore(js,fjs);}(window,document,'script','mw', 'https://cdn.theaccessplatform.com/popcard.js'));
              mw('init', {universityID: 2096,terms:{{ js_terms_array|raw }},title:"", popcardButtonText:"", alignment: "right",viewType: "standard", backgroundColor: "#4f4c4c", titleColor: "#efefef", buttonTextColor: "#ffffff",
              href:"{{- content.field_popcard_url.0|raw -}}?tap-dashboard=true&utm_medium=popcard&leadType=tap_feed{% if query_string %}&{{ query_string }}{% endif %}"});</script>
          {% endif %}

          {% if widget == 'carousel' %}
          <iframe id='tap-iframe-widget' style='border:none' src='' width='100%' height='700' scrolling='no' data-type='carousel' data-university-id='2096' data-terms='' data-params='?utm_medium=carousel&autoplay=false'></iframe><script defer src='https://cdn.theaccessplatform.com/carousel.js'></script>
          {% endif %}

          {% if widget == 'tapfeed' %}
          <iframe id='tap-iframe-widget' style='border:none' src='' width='100%' height='700' scrolling='no' data-type='full-widget' data-university-id='2096'></iframe><script defer src='https://cdn.theaccessplatform.com/widget.js'></script>
          {% endif %}

          {% if widget == 'button' %}
          <script>(function(w,d,s,o,f,js,fjs){w['button']=o;w[o]=w[o]||function(){(w[o].q=w[o].q||[]).push(arguments)};js=d.createElement(s),fjs=d.getElementsByTagName(s)[0];js.id=o;js.src=f;js.async=1;fjs.parentNode.insertBefore(js,fjs);}(window,document,'script','mw', 'https://cdn.theaccessplatform.com/button.js'));mw('init',{universityID:2096,title:"",href:"{{- content.field_popcard_url.0|raw -}}?tap-dashboard=true&utm_medium=button"});</script>
          {% endif %}
    {% endblock %}
  </div>
{% endblock paragraph %}
