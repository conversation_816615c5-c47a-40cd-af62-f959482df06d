uuid: 1c916548-2ed5-49d5-87c3-e20718137ffe
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_sport
    - paragraphs.paragraphs_type.chargers_athletic_event
    - taxonomy.vocabulary.sport
id: paragraph.chargers_athletic_event.field_sport
field_name: field_sport
entity_type: paragraph
bundle: chargers_athletic_event
label: Sport
description: 'Select one from the following list:'
required: true
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      sport: sport
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
