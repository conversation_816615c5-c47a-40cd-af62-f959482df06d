uuid: e6b63a4e-f59f-4d2e-bd6a-49aa7559ac6c
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_graphql_query
    - paragraphs.paragraphs_type.curriculum_core_graphql_
  content:
    - 'graphql_content_entity_graphql:graphql_content_entity_graphql:4d1913cf-9ad7-4cb7-9703-8e5422d8a12c'
id: paragraph.curriculum_core_graphql_.field_graphql_query
field_name: field_graphql_query
entity_type: paragraph
bundle: curriculum_core_graphql_
label: 'Graphql Query - Curriculum'
description: ''
required: false
translatable: false
default_value:
  -
    target_uuid: 4d1913cf-9ad7-4cb7-9703-8e5422d8a12c
default_value_callback: ''
settings:
  handler: 'default:graphql_content_entity_graphql'
  handler_settings:
    target_bundles: null
    sort:
      field: _none
      direction: ASC
    auto_create: false
field_type: entity_reference
