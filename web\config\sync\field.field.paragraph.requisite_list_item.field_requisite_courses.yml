uuid: 6ad001e7-1d87-4f50-989b-5c3da6b83943
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_requisite_courses
    - node.type.course
    - paragraphs.paragraphs_type.requisite_list_item
id: paragraph.requisite_list_item.field_requisite_courses
field_name: field_requisite_courses
entity_type: paragraph
bundle: requisite_list_item
label: Courses
description: 'If applicable, enter the requisite, corequisite or prerequisite courses required to take this course.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      course: course
    sort:
      field: _none
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
