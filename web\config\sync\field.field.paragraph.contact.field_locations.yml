uuid: e9298612-2f40-4bc6-98d8-e1d34ea2d7b0
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_locations
    - paragraphs.paragraphs_type.contact
    - paragraphs.paragraphs_type.contact_campus_location
  module:
    - entity_reference_revisions
id: paragraph.contact.field_locations
field_name: field_locations
entity_type: paragraph
bundle: contact
label: Locations
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    negate: 0
    target_bundles:
      contact_campus_location: contact_campus_location
    target_bundles_drag_drop:
      call_to_action:
        weight: 12
        enabled: false
      call_to_action_button:
        weight: 13
        enabled: false
      contact:
        weight: 14
        enabled: false
      contact_campus_location:
        enabled: true
        weight: 15
      image:
        weight: 16
        enabled: false
      popular_item:
        weight: 17
        enabled: false
      related_information:
        weight: 18
        enabled: false
      section_links:
        weight: 19
        enabled: false
      text_and_image:
        weight: 20
        enabled: false
      text_and_video:
        weight: 21
        enabled: false
      text_only:
        weight: 22
        enabled: false
field_type: entity_reference_revisions
