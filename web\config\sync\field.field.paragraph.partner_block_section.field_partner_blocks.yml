uuid: bf3fe5ec-2ea3-46a3-8168-ebd7fb33890b
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_partner_blocks
    - paragraphs.paragraphs_type.partner_block
    - paragraphs.paragraphs_type.partner_block_section
  module:
    - entity_reference_revisions
id: paragraph.partner_block_section.field_partner_blocks
field_name: field_partner_blocks
entity_type: paragraph
bundle: partner_block_section
label: 'Partner blocks'
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    target_bundles:
      partner_block: partner_block
    negate: 0
    target_bundles_drag_drop:
      call_to_action:
        weight: 38
        enabled: false
      call_to_action_button:
        weight: 39
        enabled: false
      camosun_journey_banner:
        weight: 40
        enabled: false
      camosun_journey_banner_container:
        weight: 41
        enabled: false
      chargers_athletic_event:
        weight: 42
        enabled: false
      coach_profile:
        weight: 43
        enabled: false
      college_event:
        weight: 44
        enabled: false
      contact:
        weight: 45
        enabled: false
      contact_campus_location:
        weight: 46
        enabled: false
      contact_information:
        weight: 47
        enabled: false
      curriculum_and_courses:
        weight: 48
        enabled: false
      curriculum_and_courses_list:
        weight: 49
        enabled: false
      curriculum_and_courses_term:
        weight: 50
        enabled: false
      custom_featured_events_list:
        weight: 51
        enabled: false
      custom_featured_news_list:
        weight: 52
        enabled: false
      ebsco_single_search:
        weight: 53
        enabled: false
      faculty_staff_profile:
        weight: 54
        enabled: false
      image:
        weight: 55
        enabled: false
      partner_block:
        weight: 56
        enabled: true
      partner_block_section:
        weight: 57
        enabled: false
      popular_item:
        weight: 58
        enabled: false
      program_requirement:
        weight: 59
        enabled: false
      related_information:
        weight: 60
        enabled: false
      requisite_list:
        weight: 62
        enabled: false
      requisite_list_item:
        weight: 63
        enabled: false
      requisites:
        weight: 61
        enabled: false
      section_links:
        weight: 64
        enabled: false
      showcase_primary_content:
        weight: 65
        enabled: false
      showcase_secondary_content:
        weight: 66
        enabled: false
      staff_faculty_profile_list:
        weight: 67
        enabled: false
      student_athlete_profile:
        weight: 68
        enabled: false
      student_athlete_profile_list:
        weight: 69
        enabled: false
      tab:
        weight: 70
        enabled: false
      tabs_content:
        weight: 71
        enabled: false
      text_and_image:
        weight: 72
        enabled: false
      text_and_video:
        weight: 73
        enabled: false
      text_only:
        weight: 74
        enabled: false
field_type: entity_reference_revisions
