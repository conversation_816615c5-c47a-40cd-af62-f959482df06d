uuid: a98fe860-c34f-4bc8-abd8-cf1fccb938ac
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.media.full
    - core.entity_view_mode.media.responsive_image
    - core.entity_view_mode.media.responsive_image_full_quarter_
    - core.entity_view_mode.media.responsive_image_full_third_
    - core.entity_view_mode.media.responsive_image_half
    - core.entity_view_mode.media.responsive_image_half_width_
    - core.entity_view_mode.media.responsive_image_third_half_
    - core.entity_view_mode.media.responsive_image_two_third_width_
  module:
    - editor
    - linkit
    - media
_core:
  default_config_hash: P8ddpAIKtawJDi5SzOwCzVnnNYqONewSTJ6Xn0dW_aQ
name: 'Basic HTML'
format: basic_html
weight: 0
filters:
  editor_file_reference:
    id: editor_file_reference
    provider: editor
    status: true
    weight: 11
    settings: {  }
  filter_align:
    id: filter_align
    provider: filter
    status: true
    weight: 7
    settings: {  }
  filter_autop:
    id: filter_autop
    provider: filter
    status: true
    weight: 0
    settings: {  }
  filter_caption:
    id: filter_caption
    provider: filter
    status: true
    weight: 8
    settings: {  }
  filter_html:
    id: filter_html
    provider: filter
    status: true
    weight: -10
    settings:
      allowed_html: '<br> <p class="blockquote-attr"> <h2 id> <h3 id> <cite> <dl> <dt> <dd> <span> <footer> <div> <style> <img src alt height width data-entity-type data-entity-uuid data-align data-caption> <svg width height viewBox version> <g stroke stroke-width fill fill-rule transform> <path d> <script async src> <ul type> <ol type start> <a hreflang accesskey target href title id rel data-entity-type data-entity-uuid data-entity-substitution> <strong> <em> <code> <blockquote> <li> <drupal-media data-entity-type data-entity-uuid alt data-view-mode data-caption data-align>'
      filter_html_help: false
      filter_html_nofollow: false
  filter_html_image_secure:
    id: filter_html_image_secure
    provider: filter
    status: true
    weight: 9
    settings: {  }
  filter_url:
    id: filter_url
    provider: filter
    status: true
    weight: 0
    settings:
      filter_url_length: 72
  linkit:
    id: linkit
    provider: linkit
    status: true
    weight: 0
    settings:
      title: true
  media_embed:
    id: media_embed
    provider: media
    status: true
    weight: 100
    settings:
      default_view_mode: responsive_image
      allowed_view_modes:
        default: default
        full: full
        responsive_image: responsive_image
        responsive_image_full_quarter_: responsive_image_full_quarter_
        responsive_image_full_third_: responsive_image_full_third_
        responsive_image_half: responsive_image_half
        responsive_image_half_width_: responsive_image_half_width_
        responsive_image_third_half_: responsive_image_third_half_
        responsive_image_two_third_width_: responsive_image_two_third_width_
      allowed_media_types:
        image: image
        remote_video: remote_video
