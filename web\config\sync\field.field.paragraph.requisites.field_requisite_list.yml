uuid: feaa87bd-f2d1-4071-8d89-fb15fc34d037
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_requisite_list
    - paragraphs.paragraphs_type.requisite_list
    - paragraphs.paragraphs_type.requisites
  module:
    - entity_reference_revisions
id: paragraph.requisites.field_requisite_list
field_name: field_requisite_list
entity_type: paragraph
bundle: requisites
label: List
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    negate: 0
    target_bundles:
      requisite_list: requisite_list
    target_bundles_drag_drop:
      call_to_action:
        weight: 15
        enabled: false
      call_to_action_button:
        weight: 16
        enabled: false
      contact:
        weight: 17
        enabled: false
      contact_campus_location:
        weight: 18
        enabled: false
      image:
        weight: 19
        enabled: false
      popular_item:
        weight: 20
        enabled: false
      related_information:
        weight: 21
        enabled: false
      requisite_list:
        enabled: true
        weight: 22
      requisite_list_item:
        weight: 23
        enabled: false
      requisites:
        weight: 24
        enabled: false
      section_links:
        weight: 25
        enabled: false
      text_and_image:
        weight: 26
        enabled: false
      text_and_video:
        weight: 27
        enabled: false
      text_only:
        weight: 28
        enabled: false
field_type: entity_reference_revisions
