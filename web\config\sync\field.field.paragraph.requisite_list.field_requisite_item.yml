uuid: 303ce396-b5f2-477d-9610-cdaaa26e803f
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_requisite_item
    - paragraphs.paragraphs_type.requisite_list
    - paragraphs.paragraphs_type.requisite_list_item
  module:
    - entity_reference_revisions
id: paragraph.requisite_list.field_requisite_item
field_name: field_requisite_item
entity_type: paragraph
bundle: requisite_list
label: 'Requisite item'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:paragraph'
  handler_settings:
    negate: 0
    target_bundles:
      requisite_list_item: requisite_list_item
    target_bundles_drag_drop:
      call_to_action:
        weight: 15
        enabled: false
      call_to_action_button:
        weight: 16
        enabled: false
      contact:
        weight: 17
        enabled: false
      contact_campus_location:
        weight: 18
        enabled: false
      image:
        weight: 19
        enabled: false
      popular_item:
        weight: 20
        enabled: false
      related_information:
        weight: 21
        enabled: false
      requisite_list:
        weight: 22
        enabled: false
      requisite_list_item:
        enabled: true
        weight: 23
      requisites:
        weight: 24
        enabled: false
      section_links:
        weight: 25
        enabled: false
      text_and_image:
        weight: 26
        enabled: false
      text_and_video:
        weight: 27
        enabled: false
      text_only:
        weight: 28
        enabled: false
field_type: entity_reference_revisions
