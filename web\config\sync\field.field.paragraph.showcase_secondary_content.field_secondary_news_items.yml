uuid: 4b108690-2468-4b80-99f9-e48073a3d44d
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_secondary_news_items
    - node.type.article
    - node.type.page
    - paragraphs.paragraphs_type.showcase_secondary_content
id: paragraph.showcase_secondary_content.field_secondary_news_items
field_name: field_secondary_news_items
entity_type: paragraph
bundle: showcase_secondary_content
label: 'Secondary News Items'
description: '2 maximum'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      page: page
      article: article
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: page
field_type: entity_reference
