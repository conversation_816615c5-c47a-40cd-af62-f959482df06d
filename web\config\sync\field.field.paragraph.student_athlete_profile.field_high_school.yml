uuid: 5a62fdf5-28fe-4194-a583-2c50f4cd717c
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_high_school
    - paragraphs.paragraphs_type.student_athlete_profile
id: paragraph.student_athlete_profile.field_high_school
field_name: field_high_school
entity_type: paragraph
bundle: student_athlete_profile
label: 'High school'
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string
