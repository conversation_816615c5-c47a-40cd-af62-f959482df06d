uuid: e8a67d6f-d8b1-466b-8e0f-a4c489fd73c2
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_text
    - paragraphs.paragraphs_type.tab
  module:
    - allowed_formats
    - text
third_party_settings:
  allowed_formats:
    basic_html: basic_html
    restricted_html: restricted_html
    full_html: full_html
    plain_text: '0'
id: paragraph.tab.field_text
field_name: field_text
entity_type: paragraph
bundle: tab
label: 'Tab content'
description: 'Enter the tab content here. This will be displayed when the tab is selected.'
required: true
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  allowed_formats: {  }
field_type: text_long
