uuid: 1b56490b-e3e3-4ff1-bb6c-cb7c0eba74a7
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.media.full
    - core.entity_view_mode.media.responsive_image
    - core.entity_view_mode.media.responsive_image_full_quarter_
    - core.entity_view_mode.media.responsive_image_full_third_
    - core.entity_view_mode.media.responsive_image_half
    - core.entity_view_mode.media.responsive_image_half_width_
    - core.entity_view_mode.media.responsive_image_third_half_
    - core.entity_view_mode.media.responsive_image_two_third_width_
  module:
    - media
_core:
  default_config_hash: oz6NyPDAB4HB6N9hgH2LwNVtCd-sXbMG1fbn5KsRIDI
name: 'Restricted HTML'
format: restricted_html
weight: 1
filters:
  filter_autop:
    id: filter_autop
    provider: filter
    status: true
    weight: 0
    settings: {  }
  filter_html:
    id: filter_html
    provider: filter
    status: true
    weight: -10
    settings:
      allowed_html: '<a href hreflang> <em> <strong> <cite> <blockquote cite> <code> <ul type> <ol start type> <li> <dl> <dt> <dd> <h2 id> <h3 id> <h4 id> <h5 id> <h6 id> <drupal-media data-entity-type data-entity-uuid data-view-mode> '
      filter_html_help: true
      filter_html_nofollow: false
  filter_url:
    id: filter_url
    provider: filter
    status: true
    weight: 0
    settings:
      filter_url_length: 72
  media_embed:
    id: media_embed
    provider: media
    status: true
    weight: 100
    settings:
      default_view_mode: responsive_image
      allowed_view_modes:
        default: default
        full: full
        responsive_image: responsive_image
        responsive_image_full_quarter_: responsive_image_full_quarter_
        responsive_image_full_third_: responsive_image_full_third_
        responsive_image_half: responsive_image_half
        responsive_image_half_width_: responsive_image_half_width_
        responsive_image_third_half_: responsive_image_third_half_
        responsive_image_two_third_width_: responsive_image_two_third_width_
      allowed_media_types:
        image: image
        remote_video: remote_video
