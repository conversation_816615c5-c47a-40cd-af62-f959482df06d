uuid: b16dee93-7af8-406d-9681-1c332505efc1
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_news_items
    - node.type.article
    - paragraphs.paragraphs_type.custom_featured_news_list
id: paragraph.custom_featured_news_list.field_news_items
field_name: field_news_items
entity_type: paragraph
bundle: custom_featured_news_list
label: 'News Items'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:node'
  handler_settings:
    target_bundles:
      article: article
    sort:
      field: _none
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
